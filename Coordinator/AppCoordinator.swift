import SwiftUI
import UIKit

enum NavigationState {
    case staging
    case batchProcessing(images: [UIImage])
    case batchVisionResults(images: [UIImage], visionResponses: [String])
    case batchGeminiProcessing(images: [UIImage], visionResponses: [String])
    #if DEBUG
    case debug(visionResponse: String, geminiResponse: String)
    #endif
    case results([Ingredient])
}

@MainActor
class AppCoordinator: ObservableObject {
    @Published var navigationState: NavigationState = .staging
    private let pantryService: PantryService
    
    init(pantryService: PantryService) {
        self.pantryService = pantryService
    }
    
    func rootView() -> some View {
        TabView {
            // Scanner Tab
            Group {
                switch navigationState {
                case .staging:
                    StagingView(viewModel: StagingViewModel(coordinator: self))
                case .batchProcessing(let images):
                    BatchProcessingView(viewModel: BatchProcessingViewModel(coordinator: self, images: images))
                case .batchVisionResults(let images, let visionResponses):
                    BatchVisionResultsView(viewModel: BatchVisionResultsViewModel(coordinator: self, images: images, visionResponses: visionResponses))
                case .batchGeminiProcessing(let images, let visionResponses):
                    BatchGeminiProcessingView(viewModel: BatchGeminiProcessingViewModel(coordinator: self, images: images, visionResponses: visionResponses))
                #if DEBUG
                case .debug(let visionResponse, let geminiResponse):
                    DebugView(viewModel: DebugViewModel(coordinator: self, visionResponse: visionResponse, geminiResponse: geminiResponse))
                #endif
                case .results(let ingredients):
                    ResultsView(viewModel: ResultsViewModel(coordinator: self, ingredients: ingredients, pantryService: self.pantryService))
                }
            }
            .tabItem {
                Label("Scan", systemImage: "barcode.viewfinder")
            }
            
            // Pantry Tab
            PantryView()
                .tabItem {
                    Label("Pantry", systemImage: "cabinet.fill")
                }
            
            // Recipe Generator Tab
            RecipeGeneratorView(pantryService: self.pantryService)
                .tabItem {
                    Label("Recipes", systemImage: "flame.fill")
                }
        }
        .environmentObject(self.pantryService)
    }
    
    // Navigation methods
    func navigateToBatchProcessing(images: [UIImage]) {
        navigationState = .batchProcessing(images: images)
    }
    
    func navigateToBatchVisionResults(images: [UIImage], visionResponses: [String]) {
        navigationState = .batchVisionResults(images: images, visionResponses: visionResponses)
    }
    
    func navigateToBatchGeminiProcessing(images: [UIImage], visionResponses: [String]) {
        navigationState = .batchGeminiProcessing(images: images, visionResponses: visionResponses)
    }
    
    #if DEBUG
    func navigateToDebug(visionResponse: String, geminiResponse: String) {
        navigationState = .debug(visionResponse: visionResponse, geminiResponse: geminiResponse)
    }
    #endif
    
    func navigateToResults(ingredients: [Ingredient]) {
        navigationState = .results(ingredients)
    }
    
    func navigateToStaging() {
        navigationState = .staging
    }
} 