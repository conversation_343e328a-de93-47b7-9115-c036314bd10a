name: IngredientScanner
options:
  bundleIdPrefix: com.example
  deploymentTarget:
    iOS: 15.0
  developmentLanguage: en
  
targets:
  IngredientScanner:
    type: application
    platform: iOS
    sources:
      - path: Application
      - path: Coordinator
      - path: Services
      - path: Models
      - path: Features
      - path: Utilities
    info:
      path: Application/Info.plist
      properties:
        CFBundleDisplayName: IngredientScanner
        CFBundleShortVersionString: "1.0"
        CFBundleVersion: "1"
        UILaunchStoryboardName: ""
        UISupportedInterfaceOrientations:
          - UIInterfaceOrientationPortrait
        LSRequiresIPhoneOS: true
    settings:
      base:
        PRODUCT_BUNDLE_IDENTIFIER: com.example.IngredientScanner
        PRODUCT_NAME: IngredientScanner
        SWIFT_VERSION: 5.0
        TARGETED_DEVICE_FAMILY: 1,2
        DEVELOPMENT_TEAM: ""
        CODE_SIGN_STYLE: Automatic
        INFOPLIST_FILE: Application/Info.plist
        ENABLE_PREVIEWS: YES
        DEVELOPMENT_ASSET_PATHS: ""
        ASSETCATALOG_COMPILER_APPICON_NAME: AppIcon
        ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME: AccentColor 