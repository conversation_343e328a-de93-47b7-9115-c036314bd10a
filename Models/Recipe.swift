import Foundation

struct RecipePreferences {
    var cookingTimeInMinutes: Int
    var numberOfServings: Int
    var dietaryRestrictions: [String] // e.g., ["Vegetarian", "Gluten-Free"]
}

struct Recipe: Codable {
    let recipeTitle: String
    let description: String
    let ingredients: [String]
    let instructions: [String]
    let nutrition: NutritionInfo
    
    struct NutritionInfo: Codable {
        let calories: String
        let protein: String
        let carbs: String
        let fat: String
    }
} 