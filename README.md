# IngredientScanner

A complete iOS application that uses computer vision and AI to scan images and extract food ingredients from receipts, ingredient lists, or refrigerator contents.

## Features

- **Image Capture**: Take photos or select from photo library
- **AI-Powered Detection**: Uses Google Vision API for text detection and Gemini API for ingredient extraction
- **Interactive Results**: Review, check/uncheck, and add ingredients to the final list
- **Debug Mode**: View raw API responses in development builds
- **MVVM-C Architecture**: Clean, scalable architecture with coordinators

## Requirements

- iOS 15.0+
- Xcode 14.0+
- Swift 5.5+
- Google Cloud Vision API key
- Google Gemini API key

## Setup Instructions

### 1. API Keys Configuration

Before running the app, you need to add your API keys:

1. Navigate to `IngredientScanner/Utilities/APIKeys.swift`
2. Replace the placeholder values with your actual API keys:

```swift
enum APIKeys {
    static let googleVisionAPIKey = "YOUR_ACTUAL_GOOGLE_VISION_API_KEY"
    static let geminiAPIKey = "YOUR_ACTUAL_GEMINI_API_KEY"
}
```

**Important**: The `APIKeys.swift` file is gitignored to protect your API keys. Never commit real API keys to version control.

### 2. Getting API Keys

#### Google Vision API:
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Cloud Vision API
4. Create credentials (API Key)
5. Copy the API key

#### Gemini API:
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create an API key
3. Copy the API key

### 3. Generating the Xcode Project

Since the project is provided as source files, you need to generate the Xcode project first:

```bash
cd IngredientScanner
./generate_xcodeproj.sh
```

This script will:
- Install xcodegen if needed (requires Homebrew)
- Generate the Xcode project file
- Provide instructions for next steps

### 4. Running the Project

1. Open `IngredientScanner.xcodeproj` in Xcode
2. Select your target device (simulator or physical device)
3. Select a development team in the project settings (required for device testing)
4. Build and run (⌘+R)

## Architecture

The app follows the MVVM-C (Model-View-ViewModel-Coordinator) pattern:

- **Models**: Simple data structures (`Ingredient`)
- **Views**: SwiftUI views for UI
- **ViewModels**: Business logic and state management
- **Coordinator**: Navigation flow management
- **Services**: Actor-based API services for thread-safe network operations

## Project Structure

```
IngredientScanner/
├── Application/          # App entry point and assets
├── Coordinator/          # Navigation coordinator
├── Services/            # API services (Actors)
├── Models/              # Data models
├── Features/            # Feature modules
│   ├── 1_ImageCapture/  # Main screen
│   ├── 2_ImagePreview/  # Image preview and processing
│   ├── 3_Results/       # Results display
│   └── Debug/           # Debug view (DEBUG builds only)
└── Utilities/           # Helper files and utilities
```

## Privacy & Permissions

The app requires the following permissions:
- **Camera**: To take photos of ingredients
- **Photo Library**: To select existing photos

These permissions will be requested when needed.

## Debug Mode

In DEBUG builds, the app includes a debug view that shows:
- Raw Google Vision API response
- Processed Gemini API response

This helps in understanding how the APIs are processing your images.

## Notes

- The app is designed to work immediately after adding valid API keys
- All network operations are handled asynchronously with proper error handling
- The UI is built entirely with SwiftUI for modern iOS development
- Images are compressed before sending to APIs to optimize performance

## Troubleshooting

1. **"No ingredients found" error**: Ensure the image clearly shows text containing food items
2. **API errors**: Verify your API keys are correct and have the necessary permissions
3. **Camera not working**: Check that camera permissions are granted in Settings

## License

This is a demo project for educational purposes. 