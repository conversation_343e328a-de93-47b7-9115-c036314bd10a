import SwiftUI

struct GeneratedRecipeDetailView: View {
    let recipe: Recipe
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Recipe Title and Description
                    VStack(alignment: .leading, spacing: 10) {
                        Text(recipe.recipeTitle)
                            .font(.largeTitle)
                            .fontWeight(.bold)
                        
                        Text(recipe.description)
                            .font(.body)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal)
                    
                    // Nutrition Info
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Nutrition Information")
                            .font(.headline)
                        
                        HStack(spacing: 15) {
                            NutritionBadge(title: "Calories", value: recipe.nutrition.calories, color: .orange)
                            NutritionBadge(title: "Protein", value: recipe.nutrition.protein, color: .red)
                            NutritionBadge(title: "Carbs", value: recipe.nutrition.carbs, color: .blue)
                            NutritionBadge(title: "Fat", value: recipe.nutrition.fat, color: .green)
                        }
                    }
                    .padding(.horizontal)
                    
                    Divider()
                    
                    // Ingredients Section
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Ingredients")
                            .font(.headline)
                        
                        ForEach(recipe.ingredients, id: \.self) { ingredient in
                            HStack {
                                Image(systemName: "circle.fill")
                                    .font(.system(size: 6))
                                    .foregroundColor(.orange)
                                
                                Text(ingredient)
                                    .font(.body)
                            }
                            .padding(.vertical, 2)
                        }
                    }
                    .padding(.horizontal)
                    
                    Divider()
                    
                    // Instructions Section
                    VStack(alignment: .leading, spacing: 15) {
                        Text("Instructions")
                            .font(.headline)
                        
                        ForEach(Array(recipe.instructions.enumerated()), id: \.offset) { index, instruction in
                            HStack(alignment: .top, spacing: 12) {
                                Text("\(index + 1)")
                                    .font(.headline)
                                    .foregroundColor(.white)
                                    .frame(width: 28, height: 28)
                                    .background(Circle().fill(Color.orange))
                                
                                Text(instruction)
                                    .font(.body)
                                    .fixedSize(horizontal: false, vertical: true)
                                
                                Spacer()
                            }
                        }
                    }
                    .padding(.horizontal)
                    .padding(.bottom, 30)
                }
                .padding(.vertical)
            }
            .navigationBarTitle("Recipe", displayMode: .inline)
            .navigationBarItems(
                trailing: Button("Done") {
                    dismiss()
                }
            )
        }
    }
}

struct NutritionBadge: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.headline)
                .foregroundColor(color)
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 8)
        .background(color.opacity(0.1))
        .cornerRadius(8)
    }
} 