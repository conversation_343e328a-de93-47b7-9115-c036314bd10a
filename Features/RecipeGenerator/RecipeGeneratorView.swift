import SwiftUI

struct RecipeGeneratorView: View {
    @StateObject private var viewModel: RecipeGeneratorViewModel
    @EnvironmentObject var pantryService: PantryService
    @State private var showingRecipeDetail = false
    
    init(pantryService: PantryService) {
        _viewModel = StateObject(wrappedValue: RecipeGeneratorViewModel(pantryService: pantryService))
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Pantry Items Section
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Your Pantry Items")
                            .font(.headline)
                        
                        if pantryService.pantryItems.isEmpty {
                            Text("Your pantry is empty. Add ingredients from the Scan tab first.")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .padding()
                                .frame(maxWidth: .infinity)
                                .background(Color.gray.opacity(0.1))
                                .cornerRadius(10)
                        } else {
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 8) {
                                    ForEach(pantryService.pantryItems) { ingredient in
                                        Text(ingredient.name)
                                            .font(.caption)
                                            .padding(.horizontal, 12)
                                            .padding(.vertical, 6)
                                            .background(Color.blue.opacity(0.1))
                                            .foregroundColor(.blue)
                                            .cornerRadius(15)
                                    }
                                }
                            }
                        }
                    }
                    .padding(.horizontal)
                    
                    Divider()
                    
                    // Preferences Section
                    VStack(alignment: .leading, spacing: 15) {
                        Text("Recipe Preferences")
                            .font(.headline)
                        
                        // Cooking Time
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Cooking Time")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            Picker("Cooking Time", selection: $viewModel.cookingTimeInMinutes) {
                                ForEach(viewModel.cookingTimeOptions, id: \.self) { minutes in
                                    Text("\(minutes) minutes").tag(minutes)
                                }
                            }
                            .pickerStyle(SegmentedPickerStyle())
                        }
                        
                        // Number of Servings
                        HStack {
                            Text("Number of Servings")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            Spacer()
                            
                            Stepper("\(viewModel.numberOfServings)", value: $viewModel.numberOfServings, in: 1...8)
                                .fixedSize()
                        }
                        
                        // Dietary Restrictions
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Dietary Restrictions")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            ForEach(Array(viewModel.dietaryRestrictions.keys.sorted()), id: \.self) { restriction in
                                Toggle(restriction, isOn: Binding(
                                    get: { viewModel.dietaryRestrictions[restriction] ?? false },
                                    set: { _ in viewModel.toggleRestriction(restriction) }
                                ))
                            }
                        }
                    }
                    .padding(.horizontal)
                    
                    // Generate Button
                    Button(action: {
                        Task {
                            await viewModel.generateRecipe()
                            if viewModel.generatedRecipe != nil {
                                showingRecipeDetail = true
                            }
                        }
                    }) {
                        HStack {
                            if viewModel.isGenerating {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                            } else {
                                Image(systemName: "flame.fill")
                            }
                            Text(viewModel.isGenerating ? "Generating..." : "Generate Recipe")
                        }
                        .font(.title3.weight(.medium))
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 14)
                        .background(viewModel.canGenerateRecipe ? Color.orange : Color.gray)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(!viewModel.canGenerateRecipe)
                    .padding(.horizontal)
                    .padding(.top, 10)
                }
                .padding(.vertical)
            }
            .navigationTitle("Recipe Generator")
            .navigationBarTitleDisplayMode(.large)
            .alert("Error", isPresented: $viewModel.showError) {
                Button("OK", role: .cancel) { }
            } message: {
                Text(viewModel.errorMessage)
            }
            .sheet(isPresented: $showingRecipeDetail) {
                if let recipe = viewModel.generatedRecipe {
                    GeneratedRecipeDetailView(recipe: recipe)
                }
            }
        }
    }
} 