import Foundation
import SwiftUI

@MainActor
class RecipeGeneratorViewModel: ObservableObject {
    @Published var cookingTimeInMinutes = 30
    @Published var numberOfServings = 2
    @Published var dietaryRestrictions: [String: Bool] = [
        "Vegetarian": false,
        "Vegan": false,
        "Gluten-Free": false,
        "Dairy-Free": false,
        "Nut-Free": false,
        "Low-Carb": false
    ]
    @Published var isGenerating = false
    @Published var generatedRecipe: Recipe?
    @Published var showError = false
    @Published var errorMessage = ""
    
    private let recipeService = RecipeGenerationService()
    private let pantryService: PantryService
    
    // Available cooking time options
    let cookingTimeOptions = [15, 30, 45, 60]
    
    init(pantryService: PantryService) {
        self.pantryService = pantryService
    }
    
    var selectedRestrictions: [String] {
        dietaryRestrictions.compactMap { key, value in
            value ? key : nil
        }
    }
    
    var canGenerateRecipe: Bool {
        !pantryService.pantryItems.isEmpty && !isGenerating
    }
    
    func generateRecipe() async {
        guard canGenerateRecipe else { return }
        
        isGenerating = true
        showError = false
        
        let ingredients = pantryService.pantryItems.map { $0.name }
        let preferences = RecipePreferences(
            cookingTimeInMinutes: cookingTimeInMinutes,
            numberOfServings: numberOfServings,
            dietaryRestrictions: selectedRestrictions
        )
        
        do {
            let recipe = try await recipeService.generateRecipe(
                from: ingredients,
                preferences: preferences
            )
            generatedRecipe = recipe
        } catch {
            errorMessage = error.localizedDescription
            showError = true
        }
        
        isGenerating = false
    }
    
    func toggleRestriction(_ restriction: String) {
        dietaryRestrictions[restriction]?.toggle()
    }
} 