import SwiftUI

#if DEBUG
@MainActor
class DebugViewModel: ObservableObject {
    private weak var coordinator: AppCoordinator?
    let visionResponse: String
    let geminiResponse: String
    
    init(coordinator: AppCoordinator, visionResponse: String, geminiResponse: String) {
        self.coordinator = coordinator
        self.visionResponse = visionResponse
        self.geminiResponse = geminiResponse
    }
    
    func continueToResults() {
        // Parse ingredients from gemini response
        let ingredientStrings = geminiResponse
            .split(separator: ",")
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }
        
        let ingredients = ingredientStrings.map { Ingredient(name: String($0)) }
        coordinator?.navigateToResults(ingredients: ingredients)
    }
}
#endif 