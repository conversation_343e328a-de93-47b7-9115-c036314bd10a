import SwiftUI

struct PantryView: View {
    @StateObject private var viewModel = PantryViewModel()
    @EnvironmentObject var pantryService: PantryService
    
    var body: some View {
        NavigationView {
            List {
                if pantryService.pantryItems.isEmpty {
                    Text("Your pantry is empty")
                        .foregroundColor(.secondary)
                        .italic()
                        .padding()
                        .frame(maxWidth: .infinity, alignment: .center)
                } else {
                    ForEach(pantryService.pantryItems) { ingredient in
                        HStack {
                            Text(ingredient.name)
                                .font(.body)
                            Spacer()
                        }
                        .padding(.vertical, 4)
                    }
                    .onDelete(perform: deleteItems)
                }
            }
            .navigationTitle("Pantry")
            .listStyle(InsetGroupedListStyle())
        }
    }
    
    private func deleteItems(at offsets: IndexSet) {
        pantryService.deletePantryItem(at: offsets)
    }
} 