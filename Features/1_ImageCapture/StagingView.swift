import SwiftUI

struct StagingView: View {
    @StateObject var viewModel: StagingViewModel
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Title
                Text("Ingredient Scanner")
                    .font(.largeTitle.weight(.bold))
                    .padding(.top)
                
                // Staging Area
                VStack(alignment: .leading, spacing: 12) {
                    Text("Selected Images (\(viewModel.stagedImages.count)/3)")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    if viewModel.stagedImages.isEmpty {
                        // Empty state
                        VStack(spacing: 20) {
                            Image(systemName: "photo.on.rectangle.angled")
                                .font(.system(size: 60))
                                .foregroundColor(.gray.opacity(0.5))
                            
                            Text("Add up to 3 images to scan")
                                .font(.title3)
                                .foregroundColor(.secondary)
                        }
                        .frame(maxWidth: .infinity)
                        .frame(height: 200)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(15)
                    } else {
                        // Image grid
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 15) {
                                ForEach(Array(viewModel.stagedImages.enumerated()), id: \.offset) { index, image in
                                    ZStack(alignment: .topTrailing) {
                                        Image(uiImage: image)
                                            .resizable()
                                            .scaledToFill()
                                            .frame(width: 120, height: 120)
                                            .clipShape(RoundedRectangle(cornerRadius: 10))
                                            .shadow(radius: 3)
                                        
                                        // Remove button
                                        Button(action: {
                                            withAnimation(.spring()) {
                                                viewModel.removeImage(at: index)
                                            }
                                        }) {
                                            Image(systemName: "xmark.circle.fill")
                                                .font(.title2)
                                                .foregroundColor(.white)
                                                .background(Circle().fill(Color.red))
                                                .shadow(radius: 2)
                                        }
                                        .offset(x: 10, y: -10)
                                    }
                                }
                            }
                            .padding(.horizontal, 5)
                        }
                        .frame(height: 130)
                    }
                }
                .padding(.horizontal)
                
                // Add buttons
                VStack(spacing: 15) {
                    HStack(spacing: 15) {
                        Button(action: viewModel.takePhoto) {
                            HStack {
                                Image(systemName: "camera.fill")
                                    .font(.title3)
                                Text("Take Photo")
                                    .font(.body.weight(.medium))
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 15)
                            .background(viewModel.canAddMoreImages ? Color.blue : Color.gray.opacity(0.5))
                            .foregroundColor(.white)
                            .cornerRadius(10)
                        }
                        .disabled(!viewModel.canAddMoreImages)
                        
                        Button(action: viewModel.chooseFromLibrary) {
                            HStack {
                                Image(systemName: "photo.on.rectangle.angled")
                                    .font(.title3)
                                Text("From Library")
                                    .font(.body.weight(.medium))
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 15)
                            .background(viewModel.canAddMoreImages ? Color.green : Color.gray.opacity(0.5))
                            .foregroundColor(.white)
                            .cornerRadius(10)
                        }
                        .disabled(!viewModel.canAddMoreImages)
                    }
                    
                    if !viewModel.canAddMoreImages {
                        Text("Maximum 3 images reached")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }
                .padding(.horizontal)
                
                Spacer()
                
                // Process button
                Button(action: viewModel.processImages) {
                    HStack {
                        Image(systemName: "sparkles")
                        Text(viewModel.processButtonText)
                    }
                    .font(.title2.weight(.semibold))
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 18)
                    .background(viewModel.stagedImages.isEmpty ? Color.gray.opacity(0.3) : Color.purple)
                    .foregroundColor(.white)
                    .cornerRadius(15)
                    .shadow(radius: viewModel.stagedImages.isEmpty ? 0 : 5)
                }
                .disabled(viewModel.stagedImages.isEmpty)
                .padding(.horizontal)
                .padding(.bottom, 30)
            }
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $viewModel.showingImagePicker) {
            MultiImagePicker(
                selectedImages: Binding(
                    get: { [] },
                    set: { viewModel.handlePickerImages($0) }
                ),
                maxSelection: viewModel.remainingSlots
            )
        }
        .fullScreenCover(isPresented: $viewModel.showingCamera) {
            ImagePicker(selectedImage: Binding(
                get: { nil },
                set: { viewModel.handleCameraImage($0) }
            ), sourceType: .camera)
        }
    }
} 