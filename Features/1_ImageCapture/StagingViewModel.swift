import SwiftUI
import UIKit
import PhotosUI

@MainActor
class StagingViewModel: ObservableObject {
    private weak var coordinator: AppCoordinator?
    @Published var stagedImages: [UIImage] = []
    @Published var showingImagePicker = false
    @Published var showingCamera = false
    @Published var isProcessing = false
    
    var canAddMoreImages: Bool {
        stagedImages.count < 3
    }
    
    var remainingSlots: Int {
        3 - stagedImages.count
    }
    
    var processButtonText: String {
        let count = stagedImages.count
        if count == 0 {
            return "Add Images to Process"
        } else if count == 1 {
            return "Process 1 Image"
        } else {
            return "Process \(count) Images"
        }
    }
    
    init(coordinator: AppCoordinator) {
        self.coordinator = coordinator
    }
    
    func addImage(_ image: UIImage) {
        guard canAddMoreImages else { return }
        stagedImages.append(image)
    }
    
    func addImages(_ images: [UIImage]) {
        let availableSlots = remainingSlots
        let imagesToAdd = Array(images.prefix(availableSlots))
        stagedImages.append(contentsOf: imagesToAdd)
    }
    
    func removeImage(at index: Int) {
        guard index >= 0 && index < stagedImages.count else { return }
        stagedImages.remove(at: index)
    }
    
    func takePhoto() {
        guard canAddMoreImages else { return }
        showingCamera = true
    }
    
    func chooseFromLibrary() {
        guard canAddMoreImages else { return }
        showingImagePicker = true
    }
    
    func processImages() {
        guard !stagedImages.isEmpty else { return }
        coordinator?.navigateToBatchProcessing(images: stagedImages)
    }
    
    func handleCameraImage(_ image: UIImage?) {
        showingCamera = false
        if let image = image {
            addImage(image)
        }
    }
    
    func handlePickerImages(_ images: [UIImage]) {
        showingImagePicker = false
        addImages(images)
    }
} 