import SwiftUI

struct BatchProcessingView: View {
    @StateObject var viewModel: BatchProcessingViewModel
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                Spacer()
                
                if viewModel.isLoading {
                    ProgressView()
                        .scaleEffect(2)
                        .progressViewStyle(CircularProgressViewStyle())
                    
                    Text(viewModel.progressText)
                        .font(.title2.weight(.semibold))
                        .padding(.top, 30)
                    
                    Text("Detecting text in your images")
                        .font(.body)
                        .foregroundColor(.secondary)
                    
                    // Progress indicator
                    ProgressView(value: Double(viewModel.processedCount), total: Double(viewModel.totalCount))
                        .progressViewStyle(LinearProgressViewStyle())
                        .padding(.horizontal, 50)
                        .padding(.top, 20)
                    
                } else if let errorMessage = viewModel.errorMessage {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.red)
                    
                    Text("Error")
                        .font(.title.weight(.bold))
                    
                    Text(errorMessage)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                
                Spacer()
            }
            .navigationBarHidden(true)
        }
    }
} 