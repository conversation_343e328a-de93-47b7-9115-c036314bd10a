import SwiftUI

struct BatchVisionResultsView: View {
    @StateObject var viewModel: BatchVisionResultsViewModel
    
    var body: some View {
        NavigationView {
            VStack {
                // Header with image counter
                HStack {
                    Text(viewModel.imageCountText)
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    HStack(spacing: 15) {
                        Button(action: viewModel.goToPreviousImage) {
                            Image(systemName: "chevron.left.circle.fill")
                                .font(.title2)
                                .foregroundColor(viewModel.canGoToPrevious ? .blue : .gray.opacity(0.5))
                        }
                        .disabled(!viewModel.canGoToPrevious)
                        
                        But<PERSON>(action: viewModel.goToNextImage) {
                            Image(systemName: "chevron.right.circle.fill")
                                .font(.title2)
                                .foregroundColor(viewModel.canGoToNext ? .blue : .gray.opacity(0.5))
                        }
                        .disabled(!viewModel.canGoToNext)
                    }
                }
                .padding()
                
                ScrollView {
                    VStack(alignment: .leading, spacing: 20) {
                        // Image preview
                        VStack {
                            Text("Analyzed Image")
                                .font(.headline.weight(.bold))
                            
                            Image(uiImage: viewModel.currentImage)
                                .resizable()
                                .scaledToFit()
                                .frame(maxHeight: 200)
                                .cornerRadius(10)
                                .shadow(radius: 3)
                        }
                        
                        Divider()
                        
                        // Vision API Results
                        VStack(alignment: .leading, spacing: 10) {
                            Text("Text Detected by Google Vision:")
                                .font(.headline.weight(.bold))
                            
                            if viewModel.currentVisionResponse.isEmpty {
                                Text("No text was detected in this image.")
                                    .italic()
                                    .foregroundColor(.secondary)
                                    .padding()
                                    .background(Color.gray.opacity(0.1))
                                    .cornerRadius(8)
                            } else {
                                Text(viewModel.currentVisionResponse)
                                    .font(.system(.body, design: .monospaced))
                                    .padding()
                                    .background(Color.blue.opacity(0.1))
                                    .cornerRadius(8)
                            }
                        }
                    }
                    .padding()
                }
                
                // Action buttons
                VStack(spacing: 15) {
                    Button(action: viewModel.proceedToGemini) {
                        HStack {
                            Image(systemName: "sparkles")
                            Text("Extract Ingredients from All Images")
                        }
                        .font(.title3.weight(.semibold))
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 15)
                        .background(viewModel.hasDetectedText ? Color.green : Color.gray.opacity(0.5))
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    .disabled(!viewModel.hasDetectedText)
                    
                    Button(action: viewModel.returnToStaging) {
                        Text("Add More Images")
                            .font(.body.weight(.medium))
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(Color.gray.opacity(0.2))
                            .foregroundColor(.primary)
                            .cornerRadius(8)
                    }
                }
                .padding()
            }
            .navigationTitle("Text Detection Results")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
} 