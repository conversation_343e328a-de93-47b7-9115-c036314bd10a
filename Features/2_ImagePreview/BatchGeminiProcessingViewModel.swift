import SwiftUI
import UIKit

@MainActor
class BatchGeminiProcessingViewModel: ObservableObject {
    private weak var coordinator: AppCoordinator?
    private let images: [UIImage]
    private let visionResponses: [String]
    private let geminiService = GeminiAPIService()
    
    @Published var isLoading = true
    @Published var errorMessage: String?
    
    init(coordinator: AppCoordinator, images: [UIImage], visionResponses: [String]) {
        self.coordinator = coordinator
        self.images = images
        self.visionResponses = visionResponses
        Task {
            await processWithGemini()
        }
    }
    
    private func processWithGemini() async {
        do {
            // Aggregate all vision responses into one text
            let aggregatedText = visionResponses
                .enumerated()
                .map { index, text in
                    "=== Image \(index + 1) ===\n\(text)"
                }
                .joined(separator: "\n\n")
            
            // Call Gemini API with aggregated text
            let geminiResponse = try await geminiService.extractIngredients(from: aggregatedText)
            
            // Check if no ingredients found (empty string)
            if geminiResponse.isEmpty {
                let ingredients: [Ingredient] = []
                coordinator?.navigateToResults(ingredients: ingredients)
                return
            }
            
            // Parse ingredients from comma-separated list
            let ingredientStrings = geminiResponse
                .split(separator: ",")
                .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
                .filter { !$0.isEmpty }
                .map { String($0) }
            
            // Remove duplicates while preserving order
            let uniqueIngredients = ingredientStrings.reduce(into: [String]()) { result, ingredient in
                if !result.contains(where: { $0.caseInsensitiveCompare(ingredient) == .orderedSame }) {
                    result.append(ingredient)
                }
            }
            
            let ingredients = uniqueIngredients.map { Ingredient(name: $0) }
            
            #if DEBUG
            // In debug mode, navigate to debug view first
            coordinator?.navigateToDebug(visionResponse: aggregatedText, geminiResponse: geminiResponse)
            #else
            // In release mode, go directly to results
            coordinator?.navigateToResults(ingredients: ingredients)
            #endif
            
        } catch {
            errorMessage = error.localizedDescription
            // Navigate back to staging on error
            DispatchQueue.main.asyncAfter(deadline: .now() + 2) { [weak self] in
                self?.coordinator?.navigateToStaging()
            }
        }
        
        isLoading = false
    }
} 