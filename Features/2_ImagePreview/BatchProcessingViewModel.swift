import SwiftUI
import UIKit

@MainActor
class BatchProcessingViewModel: ObservableObject {
    private weak var coordinator: AppCoordinator?
    private let images: [UIImage]
    private let visionService = GoogleVisionAPIService()
    
    @Published var isLoading = true
    @Published var errorMessage: String?
    @Published var processedCount = 0
    @Published var totalCount: Int
    
    var progressText: String {
        "Processing image \(processedCount + 1) of \(totalCount)..."
    }
    
    init(coordinator: AppCoordinator, images: [UIImage]) {
        self.coordinator = coordinator
        self.images = images
        self.totalCount = images.count
        Task {
            await processImages()
        }
    }
    
    private func processImages() async {
        var visionResponses: [String] = []
        
        do {
            for (index, image) in images.enumerated() {
                processedCount = index
                
                // Call Google Vision API for each image
                let visionResponse = try await visionService.detectText(in: image)
                visionResponses.append(visionResponse)
            }
            
            // Navigate to batch vision results
            coordinator?.navigateToBatchVisionResults(images: images, visionResponses: visionResponses)
            
        } catch {
            errorMessage = error.localizedDescription
            // Navigate back to staging on error
            DispatchQueue.main.asyncAfter(deadline: .now() + 2) { [weak self] in
                self?.coordinator?.navigateToStaging()
            }
        }
        
        isLoading = false
    }
} 