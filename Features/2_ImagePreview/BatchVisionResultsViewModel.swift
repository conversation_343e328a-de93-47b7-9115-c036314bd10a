import SwiftUI
import UIKit

@MainActor
class BatchVisionResultsViewModel: ObservableObject {
    private weak var coordinator: AppCoordinator?
    let images: [UIImage]
    let visionResponses: [String]
    @Published var currentImageIndex = 0
    
    var currentImage: UIImage {
        images[currentImageIndex]
    }
    
    var currentVisionResponse: String {
        visionResponses[currentImageIndex]
    }
    
    var imageCountText: String {
        "Image \(currentImageIndex + 1) of \(images.count)"
    }
    
    var canGoToPrevious: Bool {
        currentImageIndex > 0
    }
    
    var canGoToNext: Bool {
        currentImageIndex < images.count - 1
    }
    
    var hasDetectedText: Bool {
        !visionResponses.allSatisfy { $0.isEmpty }
    }
    
    init(coordinator: AppCoordinator, images: [UIImage], visionResponses: [String]) {
        self.coordinator = coordinator
        self.images = images
        self.visionResponses = visionResponses
    }
    
    func goToPreviousImage() {
        guard canGoToPrevious else { return }
        currentImageIndex -= 1
    }
    
    func goToNextImage() {
        guard canGoToNext else { return }
        currentImageIndex += 1
    }
    
    func proceedToGemini() {
        coordinator?.navigateToBatchGeminiProcessing(images: images, visionResponses: visionResponses)
    }
    
    func returnToStaging() {
        coordinator?.navigateToStaging()
    }
} 