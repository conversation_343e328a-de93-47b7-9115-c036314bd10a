import SwiftUI

struct BatchGeminiProcessingView: View {
    @StateObject var viewModel: BatchGeminiProcessingViewModel
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                Spacer()
                
                if viewModel.isLoading {
                    ProgressView()
                        .scaleEffect(2)
                        .progressViewStyle(CircularProgressViewStyle())
                    
                    Text("Extracting ingredients...")
                        .font(.title2.weight(.semibold))
                        .padding(.top, 30)
                    
                    Text("Using Gemini AI to identify food items from all detected text")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                        
                } else if let errorMessage = viewModel.errorMessage {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.red)
                    
                    Text("Processing Error")
                        .font(.title.weight(.bold))
                    
                    Text(errorMessage)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                        
                    Text("Returning to main screen...")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.top)
                }
                
                Spacer()
            }
            .navigationTitle("Processing")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
} 