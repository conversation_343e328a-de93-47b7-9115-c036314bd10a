import SwiftUI

struct ResultsView: View {
    @StateObject var viewModel: ResultsViewModel
    
    var body: some View {
        NavigationView {
            VStack {
                if viewModel.ingredients.isEmpty {
                    EmptyIngredientsView()
                } else {
                    List {
                        ForEach(viewModel.ingredients.indices, id: \.self) { index in
                            HStack {
                                Button(action: {
                                    viewModel.toggleIngredient(at: index)
                                }) {
                                    HStack {
                                        Image(systemName: viewModel.ingredients[index].isSelected ? "checkmark.square.fill" : "square")
                                            .foregroundColor(viewModel.ingredients[index].isSelected ? .green : .gray)
                                            .font(.title2)
                                        
                                        Text(viewModel.ingredients[index].name)
                                            .font(.body)
                                            .strikethrough(!viewModel.ingredients[index].isSelected)
                                            .foregroundColor(viewModel.ingredients[index].isSelected ? .primary : .secondary)
                                        
                                        Spacer()
                                    }
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                            .padding(.vertical, 4)
                        }
                    }
                    .listStyle(InsetGroupedListStyle())
                    
                    VStack(spacing: 15) {
                        Button(action: { 
                            viewModel.addToPantry() 
                        }) {
                            HStack {
                                Image(systemName: viewModel.hasAddedToPantry ? "checkmark.circle.fill" : "tray.and.arrow.down.fill")
                                Text(viewModel.hasAddedToPantry ? "Added to Pantry" : "Add to Pantry")
                            }
                            .font(.title3.weight(.medium))
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(viewModel.hasAddedToPantry ? Color.green : Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                        }
                        .disabled(viewModel.getSelectedIngredients().isEmpty || viewModel.hasAddedToPantry)
                        
                        Button(action: { viewModel.showingAddIngredient = true }) {
                            HStack {
                                Image(systemName: "plus.circle.fill")
                                Text("Add Ingredient")
                            }
                            .font(.title3.weight(.medium))
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                        }
                        
                        Text("\(viewModel.getSelectedIngredients().count) of \(viewModel.ingredients.count) ingredients selected")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                }
            }
            .navigationTitle("Ingredients Found")
            .navigationBarTitleDisplayMode(.large)
            .navigationBarItems(
                trailing: Button("Rescan") {
                    viewModel.takeNewPhoto()
                }
            )
        }
        .alert("No Ingredients Detected", isPresented: $viewModel.showingNoIngredientsAlert) {
            Button("Rescan") {
                viewModel.takeNewPhoto()
            }
        } message: {
            Text("We couldn't find any food items in your image. Please try again.")
        }
        .sheet(isPresented: $viewModel.showingAddIngredient) {
            AddIngredientView(
                ingredientName: $viewModel.newIngredientName,
                onAdd: viewModel.addIngredient,
                onCancel: { viewModel.showingAddIngredient = false }
            )
        }
    }
}

struct EmptyIngredientsView: View {
    var body: some View {
        VStack(spacing: 20) {
            Spacer()
            Image(systemName: "tray")
                .font(.system(size: 80))
                .foregroundColor(.gray)
            Text("No ingredients found")
                .font(.title2.weight(.semibold))
            Text("Try taking a clearer photo of your ingredients")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            Spacer()
        }
        .padding()
    }
}

struct AddIngredientView: View {
    @Binding var ingredientName: String
    let onAdd: () -> Void
    let onCancel: () -> Void
    @FocusState private var isFocused: Bool
    
    var body: some View {
        NavigationView {
            VStack {
                Form {
                    Section(header: Text("New Ingredient")) {
                        TextField("Ingredient name", text: $ingredientName)
                            .focused($isFocused)
                            .onSubmit {
                                onAdd()
                            }
                    }
                }
                .onAppear {
                    isFocused = true
                }
            }
            .navigationTitle("Add Ingredient")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("Cancel") {
                    onCancel()
                },
                trailing: Button("Add") {
                    onAdd()
                }
                .disabled(ingredientName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            )
        }
    }
} 