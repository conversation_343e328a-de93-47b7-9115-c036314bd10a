import SwiftUI

@MainActor
class ResultsViewModel: ObservableObject {
    private weak var coordinator: AppCoordinator?
    private let pantryService: PantryService
    @Published var ingredients: [Ingredient]
    @Published var showingAddIngredient = false
    @Published var newIngredientName = ""
    @Published var showingNoIngredientsAlert = false
    @Published var hasAddedToPantry = false
    
    init(coordinator: AppCoordinator, ingredients: [Ingredient], pantryService: PantryService) {
        self.coordinator = coordinator
        self.ingredients = ingredients
        self.pantryService = pantryService
        
        // Check if no ingredients were found
        if ingredients.isEmpty {
            showingNoIngredientsAlert = true
        }
    }
    
    func toggleIngredient(at index: Int) {
        ingredients[index].isSelected.toggle()
    }
    
    func addIngredient() {
        let trimmedName = newIngredientName.trimmingCharacters(in: .whitespacesAndNewlines)
        if !trimmedName.isEmpty {
            ingredients.append(Ingredient(name: trimmedName))
            newIngredientName = ""
            showingAddIngredient = false
        }
    }
    
    func takeNewPhoto() {
        coordinator?.navigateToStaging()
    }
    
    func getSelectedIngredients() -> [Ingredient] {
        ingredients.filter { $0.isSelected }
    }
    
    func addToPantry() {
        let selectedIngredients = getSelectedIngredients()
        if !selectedIngredients.isEmpty {
            pantryService.addIngredients(selectedIngredients)
            hasAddedToPantry = true
        }
    }
} 