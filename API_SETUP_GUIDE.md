# API Setup Guide

## 🚨 **Current Issue**
Your app is showing "Invalid response from server" because the API keys are not configured.

## 🔑 **Step 1: Get Google Vision API Key**

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the **Vision API**:
   - Go to "APIs & Services" > "Library"
   - Search for "Vision API"
   - Click "Enable"
4. Create API Key:
   - Go to "APIs & Services" > "Credentials"  
   - Click "+ CREATE CREDENTIALS" > "API key"
   - Copy the generated key

## 🤖 **Step 2: Get Gemini API Key**

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Click "Create API key"
3. Copy the generated key

## ⚙️ **Step 3: Configure Your App**

1. Open `Utilities/APIKeys.swift`
2. Replace the placeholder values:

```swift
enum APIKeys {
    static let googleVisionAPIKey = "AIzaSyC-your-actual-google-vision-key"
    static let geminiAPIKey = "AIzaSyD-your-actual-gemini-key"  
}
```

## ✅ **Step 4: Test**

1. Build and run the app
2. Take a photo or select from library
3. Press "Looks Good"
4. You should now see the processing screen working

## 🛠 **Troubleshooting**

- **403 Error**: API key is invalid or doesn't have proper permissions
- **Quota Exceeded**: You've hit API limits (usually means it's working!)
- **Network Error**: Check your internet connection

## 💡 **Free Tier Limits**

- **Google Vision**: 1,000 requests/month free
- **Gemini**: Rate limited but generous free tier

Your API keys will be kept private (they're in .gitignore)! 