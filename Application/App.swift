import SwiftUI

@main
struct IngredientScannerApp: App {
    @StateObject private var pantryService = PantryService()
    @StateObject private var appCoordinator: AppCoordinator
    
    init() {
        let service = PantryService()
        _pantryService = StateObject(wrappedValue: service)
        _appCoordinator = StateObject(wrappedValue: AppCoordinator(pantryService: service))
    }
    
    var body: some Scene {
        WindowGroup {
            appCoordinator.rootView()
                .environmentObject(appCoordinator)
        }
    }
} 