import Foundation

actor RecipeGenerationService {
    private let geminiService = GeminiAPIService()
    
    func generateRecipe(from ingredients: [String], preferences: RecipePreferences) async throws -> Recipe {
        let prompt = constructRecipePrompt(ingredients: ingredients, preferences: preferences)
        
        do {
            let jsonResponse = try await geminiService.processText(prompt)
            
            // Extract JSON from the response
            // Sometimes Gemini might include extra text, so we need to extract the JSON object
            guard let jsonData = extractJSON(from: jsonResponse) else {
                throw RecipeGenerationError.invalidJSONResponse
            }
            
            let decoder = JSONDecoder()
            let recipe = try decoder.decode(Recipe.self, from: jsonData)
            return recipe
        } catch let error as DecodingError {
            print("Decoding error: \(error)")
            throw RecipeGenerationError.decodingFailed(error.localizedDescription)
        } catch {
            throw error
        }
    }
    
    private func constructRecipePrompt(ingredients: [String], preferences: RecipePreferences) -> String {
        let ingredientsList = ingredients.joined(separator: ", ")
        let restrictionsList = preferences.dietaryRestrictions.isEmpty ? "None" : preferences.dietaryRestrictions.joined(separator: ", ")
        
        return """
        You are an expert culinary AI. Your task is to create a single, delicious recipe based on a list of available ingredients and user preferences.
        
        **Rules:**
        1. The recipe must primarily use the "Available Ingredients". You may include 1-2 common pantry staples (like oil, salt, pepper, water) if necessary.
        2. Adhere strictly to all "User Preferences".
        3. Your response **MUST** be a single, valid JSON object that conforms to the provided structure. Do not include any text or markdown before or after the JSON object.
        
        **JSON Output Structure:**
        {
          "recipeTitle": "String",
          "description": "A brief, enticing description of the dish.",
          "ingredients": ["String"],
          "instructions": ["String"],
          "nutrition": {
            "calories": "String",
            "protein": "String (e.g., '25g')",
            "carbs": "String (e.g., '40g')",
            "fat": "String (e.g., '15g')"
          }
        }
        
        ---
        **Available Ingredients:**
        \(ingredientsList)
        
        **User Preferences:**
        {
          "cookingTimeInMinutes": \(preferences.cookingTimeInMinutes),
          "numberOfServings": \(preferences.numberOfServings),
          "dietaryRestrictions": [\(restrictionsList)]
        }
        ---
        
        Now, generate the recipe as a valid JSON object.
        """
    }
    
    private func extractJSON(from text: String) -> Data? {
        // First, try to parse the entire response as JSON
        if let data = text.data(using: .utf8) {
            do {
                // Test if it's valid JSON
                _ = try JSONSerialization.jsonObject(with: data, options: [])
                return data
            } catch {
                // If not, try to extract JSON from the text
            }
        }
        
        // Try to find JSON object in the text
        if let startRange = text.range(of: "{"),
           let endRange = text.range(of: "}", options: .backwards),
           startRange.lowerBound <= endRange.lowerBound {
            let jsonString = String(text[startRange.lowerBound...endRange.upperBound])
            return jsonString.data(using: .utf8)
        }
        
        return nil
    }
}

enum RecipeGenerationError: LocalizedError {
    case invalidJSONResponse
    case decodingFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidJSONResponse:
            return "The AI response did not contain valid JSON"
        case .decodingFailed(let message):
            return "Failed to decode recipe: \(message)"
        }
    }
} 