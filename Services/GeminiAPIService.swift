import Foundation

actor GeminiAPIService {
    private let apiKey = APIKeys.geminiAPIKey
    private let baseURL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent"
    
    func extractIngredients(from text: String) async throws -> String {
        // Check if API key is configured
        guard apiKey != "YOUR_GEMINI_API_KEY_HERE" && !apiKey.isEmpty else {
            throw APIError.apiKeyNotConfigured("Gemini API key not configured. Please update APIKeys.swift")
        }
        
        let prompt = """
        You are an expert culinary assistant specializing in cleaning up messy, raw Optical Character Recognition (OCR) data from grocery receipts and ingredient lists. Your task is to meticulously analyze the provided text, identify only the food items and ingredients, correct obvious errors, and return a clean, standardized list.

        Core Instructions & Rules:

        1. Identify & Extract: Your primary goal is to identify and extract anything that is an edible food item, beverage, or a direct cooking ingredient.
        2. Correct Obvious OCR Errors: You must correct common spelling mistakes or incomplete words that are clearly OCR errors. For example, "butter croiss" should become "Butter Croissant", and "avocdo" should become "Avocado".
        3. Standardize Items: Where appropriate, standardize items for clarity. For example, "org. milk" should become "Organic Milk".
        4. English Only: Process and return only English words and phrases. Completely ignore all text from other languages.

        Strict Exclusion Rules (What to IGNORE):

        * DO NOT extract prices, totals, tax amounts, or any numbers not directly part of a product name (e.g., ignore "1.99", "$5.00", "TAX").
        * DO NOT extract non-food items (e.g., "Paper Towels", "Batteries", "Foil", "Napkins").
        * DO NOT extract store names, addresses, phone numbers, or dates.
        * DO NOT extract abbreviations, SKUs, quantity codes, or cryptic receipt codes. This is critical. Ignore items like "U/S QTRS", "V", "T/C", "REF", "QTY", "EA", etc.
        * DO NOT extract conversational text like "Thank you for shopping", "Welcome", or "Loyalty ID".

        Output Format:

        * Return the results as a single, comma-separated string.
        * Each item in the list should be properly capitalized (e.g., "Avocado", not "avocado").
        * Do not include quantities, numbers, or bullet points in the final output.
        * If no food items are found in the provided text, you must return an empty string.

        ---
        Example:

        Given this raw OCR data:
        "STORE #************\\nAVOCADO HASS .99\\n1 BUNCH BANANAS 1.29\\nORG. Mlilk 1GAL 4.50\\nbutter croiss 2.50\\nU/S QTRS 1\\nT/C 5.99\\nLeche 2.00\\nTHANK YOU!"

        Your expected output MUST be:
        "Avocado, Banana, Organic Milk, Butter Croissant"
        ---

        Now, process the following data based on all the rules above:
        
        \(text)
        """
        
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": prompt]
                    ]
                ]
            ]
        ]
        
        guard let url = URL(string: "\(baseURL)?key=\(apiKey)") else {
            throw APIError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 else {
            if httpResponse.statusCode == 403 {
                throw APIError.apiKeyNotConfigured("Gemini API key is invalid or access denied (403)")
            } else {
                throw APIError.invalidResponse
            }
        }
        
        guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let candidates = json["candidates"] as? [[String: Any]],
              let firstCandidate = candidates.first,
              let content = firstCandidate["content"] as? [String: Any],
              let parts = content["parts"] as? [[String: Any]],
              let text = parts.first?["text"] as? String else {
            throw APIError.parsingError
        }
        
        return text.trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    func processText(_ prompt: String) async throws -> String {
        // Check if API key is configured
        guard apiKey != "YOUR_GEMINI_API_KEY_HERE" && !apiKey.isEmpty else {
            throw APIError.apiKeyNotConfigured("Gemini API key not configured. Please update APIKeys.swift")
        }
        
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": prompt]
                    ]
                ]
            ]
        ]
        
        guard let url = URL(string: "\(baseURL)?key=\(apiKey)") else {
            throw APIError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 else {
            if httpResponse.statusCode == 403 {
                throw APIError.apiKeyNotConfigured("Gemini API key is invalid or access denied (403)")
            } else {
                throw APIError.invalidResponse
            }
        }
        
        guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let candidates = json["candidates"] as? [[String: Any]],
              let firstCandidate = candidates.first,
              let content = firstCandidate["content"] as? [String: Any],
              let parts = content["parts"] as? [[String: Any]],
              let text = parts.first?["text"] as? String else {
            throw APIError.parsingError
        }
        
        return text.trimmingCharacters(in: .whitespacesAndNewlines)
    }
} 