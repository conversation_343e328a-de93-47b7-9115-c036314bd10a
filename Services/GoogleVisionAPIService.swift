import Foundation
import UIKit

actor GoogleVisionAPIService {
    private let apiKey = APIKeys.googleVisionAPIKey
    private let baseURL = "https://vision.googleapis.com/v1/images:annotate"
    
    func detectText(in image: UIImage) async throws -> String {
        // Check if API key is configured
        guard apiKey != "YOUR_GOOGLE_API_KEY_HERE" && !apiKey.isEmpty else {
            throw APIError.apiKeyNotConfigured("Google Vision API key not configured. Please update APIKeys.swift")
        }
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            throw APIError.invalidImage
        }
        
        let base64Image = imageData.base64EncodedString()
        
        let requestBody: [String: Any] = [
            "requests": [
                [
                    "image": [
                        "content": base64Image
                    ],
                    "features": [
                        [
                            "type": "TEXT_DETECTION",
                            "maxResults": 1
                        ]
                    ]
                ]
            ]
        ]
        
        guard let url = URL(string: "\(baseURL)?key=\(apiKey)") else {
            throw APIError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 else {
            if httpResponse.statusCode == 403 {
                throw APIError.apiKeyNotConfigured("Google Vision API key is invalid or access denied (403)")
            } else {
                throw APIError.invalidResponse
            }
        }
        
        guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let responses = json["responses"] as? [[String: Any]],
              let firstResponse = responses.first,
              let textAnnotations = firstResponse["textAnnotations"] as? [[String: Any]],
              let fullText = textAnnotations.first?["description"] as? String else {
            throw APIError.noTextFound
        }
        
        return fullText
    }
}

enum APIError: LocalizedError {
    case invalidImage
    case invalidURL
    case invalidResponse
    case noTextFound
    case parsingError
    case apiKeyNotConfigured(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidImage:
            return "Could not process the selected image"
        case .invalidURL:
            return "Invalid API URL"
        case .invalidResponse:
            return "Invalid response from server. Please check your API keys."
        case .noTextFound:
            return "No text found in image"
        case .parsingError:
            return "Could not parse the response"
        case .apiKeyNotConfigured(let message):
            return message
        }
    }
} 